#!/usr/bin/env python3
"""
Test script for the wall_jump scenario
"""

import numpy as np
import sys
import os

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_wall_jump_scenario():
    """Test the wall_jump scenario creation and basic functionality"""
    
    try:
        # Import the scenario
        from gym_art.quadrotor_multi.scenarios.wall_jump import Scenario_wall_jump
        print("✓ Successfully imported Scenario_wall_jump")
        
        # Create mock environment objects
        class MockEnv:
            def __init__(self):
                self.control_freq = 50
                self.ep_time = 10.0
        
        # Create mock envs
        num_agents = 4
        envs = [MockEnv() for _ in range(num_agents)]
        room_dims = [10.0, 10.0, 10.0]
        
        # Create scenario
        scenario = Scenario_wall_jump(
            quads_mode='wall_jump',
            envs=envs,
            num_agents=num_agents,
            room_dims=room_dims
        )
        print("✓ Successfully created wall_jump scenario")
        
        # Test reset
        scenario.reset()
        print("✓ Successfully reset scenario")
        
        # Check that goals were generated
        assert scenario.goals is not None, "Goals should be generated"
        assert len(scenario.goals) == num_agents, f"Should have {num_agents} goals"
        print(f"✓ Generated {len(scenario.goals)} goals")
        
        # Check that spawn points were generated
        assert scenario.spawn_points is not None, "Spawn points should be generated"
        assert len(scenario.spawn_points) == num_agents, f"Should have {num_agents} spawn points"
        print(f"✓ Generated {len(scenario.spawn_points)} spawn points")
        
        # Check that wall obstacles were generated
        wall_obstacles = scenario.get_wall_obstacles()
        assert len(wall_obstacles) > 0, "Wall obstacles should be generated"
        print(f"✓ Generated {len(wall_obstacles)} wall obstacles")
        
        # Check wall height is within bounds
        assert scenario.wall_min_height <= scenario.wall_height <= scenario.wall_max_height, \
            "Wall height should be within bounds"
        print(f"✓ Wall height: {scenario.wall_height:.2f}m")
        
        # Check that all goals are the same (single goal scenario)
        goal_positions = scenario.goals
        first_goal = goal_positions[0]
        for i, goal in enumerate(goal_positions):
            assert np.allclose(goal, first_goal), f"All goals should be the same, but goal {i} differs"
        print("✓ All drones have the same goal (single goal scenario)")
        
        # Check that goal is behind the wall
        goal_x = first_goal[0]
        wall_x = scenario.wall_x_position
        assert goal_x < wall_x, f"Goal should be behind wall (goal_x={goal_x}, wall_x={wall_x})"
        print("✓ Goal is positioned behind the wall")
        
        # Check that goal is below wall height
        goal_z = first_goal[2]
        max_goal_height = scenario.wall_height - scenario.goal_height_margin
        assert goal_z <= max_goal_height, f"Goal should be below wall height (goal_z={goal_z}, max={max_goal_height})"
        print("✓ Goal is positioned below wall height")
        
        # Check that spawn points are in front of wall
        for i, spawn_point in enumerate(scenario.spawn_points):
            spawn_x = spawn_point[0]
            assert spawn_x > wall_x, f"Spawn point {i} should be in front of wall (spawn_x={spawn_x}, wall_x={wall_x})"
        print("✓ All spawn points are in front of the wall")
        
        # Test step function
        scenario.step()
        print("✓ Step function works")
        
        print("\n🎉 All tests passed! Wall jump scenario is working correctly.")
        
        # Print scenario details
        print(f"\nScenario Details:")
        print(f"  Wall height: {scenario.wall_height:.2f}m")
        print(f"  Wall position: x={scenario.wall_x_position}")
        print(f"  Goal position: {first_goal}")
        print(f"  Number of wall obstacles: {len(wall_obstacles)}")
        print(f"  Spawn points range: x={min(p[0] for p in scenario.spawn_points):.2f} to {max(p[0] for p in scenario.spawn_points):.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scenario_import():
    """Test that the scenario can be imported through the mix system"""
    try:
        from gym_art.quadrotor_multi.scenarios.mix import create_scenario
        
        # Create mock environment objects
        class MockEnv:
            def __init__(self):
                self.control_freq = 50
                self.ep_time = 10.0
        
        # Create mock envs
        num_agents = 4
        envs = [MockEnv() for _ in range(num_agents)]
        room_dims = [10.0, 10.0, 10.0]
        
        # Test creating scenario through mix system
        scenario = create_scenario(
            quads_mode='wall_jump',
            envs=envs,
            num_agents=num_agents,
            room_dims=room_dims
        )
        
        print("✓ Successfully created wall_jump scenario through mix system")
        print(f"  Scenario type: {type(scenario)}")
        print(f"  Scenario name: {scenario.name()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Mix system test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Testing wall_jump scenario...")
    print("=" * 50)
    
    # Test basic scenario functionality
    test1_passed = test_wall_jump_scenario()
    
    print("\n" + "=" * 50)
    print("Testing scenario import through mix system...")
    
    # Test import through mix system
    test2_passed = test_scenario_import()
    
    print("\n" + "=" * 50)
    if test1_passed and test2_passed:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)
