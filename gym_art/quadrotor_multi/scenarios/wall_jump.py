import numpy as np
from gym_art.quadrotor_multi.scenarios.base import QuadrotorScenario


class Scenario_wall_jump(QuadrotorScenario):
    """
    Wall jumping scenario where drones must jump over a wall to reach a goal.
    
    Features:
    - Wall height varies each episode
    - Goal is behind the wall and lower than wall height
    - Wall spans across the room with side walls (U-shape)
    - Single goal for all drones
    """
    
    def __init__(self, quads_mode, envs, num_agents, room_dims):
        super().__init__(quads_mode, envs, num_agents, room_dims)
        
        # Wall parameters
        self.wall_thickness = 0.5  # Wall thickness
        self.wall_min_height = 1.5  # Minimum wall height
        self.wall_max_height = 4.0  # Maximum wall height
        self.wall_height = 2.5  # Current wall height (will be randomized)
        
        # Wall position (centered in room, perpendicular to X axis)
        self.wall_x_position = 0.0  # Wall crosses X=0
        self.wall_y_span = room_dims[1] * 0.8  # Wall spans 80% of room width
        
        # Side walls (to prevent going around)
        self.side_wall_length = 2.0  # Length of side walls extending backward
        
        # Goal parameters
        self.goal_distance_behind_wall = 3.0  # How far behind wall to place goal
        self.goal_height_margin = 0.5  # Goal will be at least this much below wall height
        
        # Starting position (in front of wall)
        self.start_distance_from_wall = 3.0  # How far in front of wall drones start
        
        # Wall obstacle positions (will be generated)
        self.wall_obstacles = []
        
    def generate_wall_obstacles(self):
        """Generate obstacle positions to create a U-shaped wall"""
        obstacles = []
        
        # Main wall (perpendicular to X axis)
        wall_y_start = -self.wall_y_span / 2
        wall_y_end = self.wall_y_span / 2
        
        # Create main wall segments
        num_wall_segments = int(self.wall_y_span / self.wall_thickness) + 1
        for i in range(num_wall_segments):
            y_pos = wall_y_start + i * self.wall_thickness
            if y_pos <= wall_y_end:
                obstacles.append([self.wall_x_position, y_pos, self.wall_height / 2])
        
        # Create left side wall
        for i in range(int(self.side_wall_length / self.wall_thickness)):
            x_pos = self.wall_x_position - (i + 1) * self.wall_thickness
            y_pos = wall_y_start
            obstacles.append([x_pos, y_pos, self.wall_height / 2])
        
        # Create right side wall  
        for i in range(int(self.side_wall_length / self.wall_thickness)):
            x_pos = self.wall_x_position - (i + 1) * self.wall_thickness
            y_pos = wall_y_end
            obstacles.append([x_pos, y_pos, self.wall_height / 2])
            
        return obstacles
    
    def generate_start_positions(self):
        """Generate starting positions in front of the wall"""
        start_x = self.wall_x_position + self.start_distance_from_wall
        
        # Generate positions in a formation in front of the wall
        start_center = np.array([start_x, 0.0, 2.0])
        
        # Use the base class formation generation but override the center
        return self.generate_goals(num_agents=self.num_agents, 
                                 formation_center=start_center,
                                 layer_dist=0.0)
    
    def generate_goal_position(self):
        """Generate single goal position behind the wall"""
        goal_x = self.wall_x_position - self.goal_distance_behind_wall
        goal_y = np.random.uniform(-1.0, 1.0)  # Random Y position
        
        # Goal height should be below wall height but above ground
        max_goal_height = self.wall_height - self.goal_height_margin
        goal_z = np.random.uniform(0.5, max(0.6, max_goal_height))
        
        return np.array([goal_x, goal_y, goal_z])
    
    def update_formation_size(self, new_formation_size):
        """Override to prevent formation size changes during episode"""
        pass
    
    def step(self):
        """No dynamic changes during episode"""
        return
    
    def reset(self, obst_map=None, cell_centers=None):
        """Reset the wall jumping scenario"""
        # Randomize wall height for this episode
        self.wall_height = np.random.uniform(self.wall_min_height, self.wall_max_height)

        # Reset formation and related parameters
        self.update_formation_and_relate_param()

        # Generate wall obstacles
        self.wall_obstacles = self.generate_wall_obstacles()

        # Generate starting positions (in front of wall)
        start_positions = self.generate_start_positions()
        self.spawn_points = start_positions

        # Generate single goal position (behind wall)
        goal_position = self.generate_goal_position()

        # All drones have the same goal
        self.goals = np.array([goal_position for _ in range(self.num_agents)])

        # Set formation center for consistency
        self.formation_center = goal_position
        
    def get_wall_obstacles(self):
        """Return wall obstacle positions for the environment"""
        return self.wall_obstacles
